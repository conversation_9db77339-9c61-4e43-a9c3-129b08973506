import os
import pytesseract
from docx import Document
from PIL import Image, ImageEnhance, ImageFilter
from docx.shared import Inches
from docx.enum.table import WD_TABLE_ALIGNMENT
import re

# Import enhanced equation utilities
try:
    from equation_utils import insert_formatted_math_text, latex_to_linear, is_mathematical_content
    EQUATION_UTILS_AVAILABLE = True
except ImportError:
    EQUATION_UTILS_AVAILABLE = False
    print("⚠️  Enhanced equation utilities not available")

# Try to import LaTeX OCR - install with: pip install pix2tex
try:
    from pix2tex.cli import LatexOCR
    LATEX_OCR_AVAILABLE = True
    print("LaTeX OCR available - will use for mathematical content")
except ImportError:
    LATEX_OCR_AVAILABLE = False
    print("LaTeX OCR not available. Install with: pip install pix2tex")

def preprocess_image_for_table(img):
    """Enhance image for better table OCR"""
    # Convert to grayscale if needed
    if img.mode != 'L':
        img = img.convert('L')
    
    # Enhance contrast
    enhancer = ImageEnhance.Contrast(img)
    img = enhancer.enhance(1.5)
    
    # Enhance sharpness
    enhancer = ImageEnhance.Sharpness(img)
    img = enhancer.enhance(2.0)
    
    # Apply slight blur to reduce noise
    img = img.filter(ImageFilter.MedianFilter(size=3))
    
    return img

def extract_math_with_latex_ocr(img):
    """Extract mathematical expressions using LaTeX OCR"""
    if not LATEX_OCR_AVAILABLE:
        return None

    try:
        # Initialize LaTeX OCR model
        model = LatexOCR()

        # Convert PIL image to format expected by LaTeX OCR
        latex_result = model(img)

        return latex_result
    except Exception as e:
        print(f"LaTeX OCR failed: {e}")
        return None

def is_mathematical_content(text):
    """Detect if text contains mathematical expressions"""
    math_patterns = [
        r'[xy]\s*[+\-=]\s*\d',  # Variables with operations
        r'\d+[xy]',  # Coefficients with variables
        r'[=<>≤≥]',  # Mathematical operators
        r'\d+/\d+',  # Fractions
        r'[αβγδεζηθικλμνξοπρστυφχψω]',  # Greek letters
        r'\^|\_{|}',  # Superscripts/subscripts
        r'\\[a-zA-Z]+',  # LaTeX commands
    ]

    return any(re.search(pattern, text) for pattern in math_patterns)

def latex_to_linear(latex_expr):
    """Convert LaTeX expression to Word Linear equation format"""
    if not latex_expr:
        return latex_expr

    # Remove LaTeX delimiters if present
    latex_expr = latex_expr.strip('$')

    # Basic LaTeX to Linear conversions
    conversions = {
        # Fractions
        r'\\frac\{([^}]+)\}\{([^}]+)\}': r'(\1)/(\2)',
        r'(\d+)/(\d+)': r'(\1)/(\2)',

        # Superscripts and subscripts
        r'\^(\d+)': r'^(\1)',
        r'_(\d+)': r'_(\1)',
        r'\^{([^}]+)}': r'^(\1)',
        r'_{([^}]+)}': r'_(\1)',

        # Greek letters
        r'\\alpha': 'α',
        r'\\beta': 'β',
        r'\\gamma': 'γ',
        r'\\delta': 'δ',
        r'\\epsilon': 'ε',
        r'\\theta': 'θ',
        r'\\lambda': 'λ',
        r'\\mu': 'μ',
        r'\\pi': 'π',
        r'\\sigma': 'σ',
        r'\\phi': 'φ',
        r'\\omega': 'ω',

        # Mathematical operators
        r'\\leq': '≤',
        r'\\geq': '≥',
        r'\\neq': '≠',
        r'\\approx': '≈',
        r'\\pm': '±',
        r'\\times': '×',
        r'\\div': '÷',
        r'\\cdot': '·',

        # Integrals and sums
        r'\\int': '∫',
        r'\\sum': '∑',
        r'\\prod': '∏',

        # Square roots
        r'\\sqrt\{([^}]+)\}': r'√(\1)',

        # Remove remaining backslashes and braces for simple expressions
        r'\\([a-zA-Z]+)': r'\1',
        r'\{([^}]+)\}': r'\1',
    }

    # Apply conversions
    linear_expr = latex_expr
    for pattern, replacement in conversions.items():
        linear_expr = re.sub(pattern, replacement, linear_expr)

    return linear_expr

def insert_equation_in_cell(cell, equation_text):
    """Insert a mathematical equation into a Word table cell"""
    if EQUATION_UTILS_AVAILABLE:
        # Use enhanced equation utilities
        return insert_formatted_math_text(cell, equation_text)
    else:
        # Fallback implementation
        try:
            # Basic LaTeX to text conversion
            linear_eq = equation_text.strip('$')

            # Simple conversions
            linear_eq = linear_eq.replace('\\frac{', '(').replace('}{', ')/(').replace('}', ')')
            linear_eq = linear_eq.replace('\\alpha', 'α').replace('\\beta', 'β').replace('\\gamma', 'γ')
            linear_eq = linear_eq.replace('\\leq', '≤').replace('\\geq', '≥').replace('\\neq', '≠')

            # Clear the cell and add formatted text
            cell.text = ""
            paragraph = cell.paragraphs[0]
            run = paragraph.add_run(linear_eq)
            run.italic = True

            print(f"  📝 Inserted basic formatted equation: {linear_eq}")
            return True

        except Exception as e:
            # Final fallback: plain text
            cell.text = equation_text
            print(f"  ⚠️  Inserted as plain text: {equation_text} (Error: {e})")
            return False

def extract_table_from_ocr_data(data, original_img=None):
    """Extract table structure from OCR data with LaTeX OCR enhancement"""
    try:
        # Get text, coordinates, and confidence
        texts = data['text']
        lefts = data['left']
        tops = data['top']
        widths = data['width']
        heights = data['height']
        confs = data['conf']

        # Filter out low confidence and empty text
        valid_data = []
        for i, text in enumerate(texts):
            if text.strip() and confs[i] > 30:  # Only keep confident text
                valid_data.append({
                    'text': text.strip(),
                    'left': lefts[i],
                    'top': tops[i],
                    'right': lefts[i] + widths[i],
                    'bottom': tops[i] + heights[i],
                    'width': widths[i],
                    'height': heights[i]
                })

        if not valid_data:
            return None

        # Sort by vertical position (top coordinate)
        valid_data.sort(key=lambda x: x['top'])

        # Group into rows based on vertical proximity
        rows = []
        current_row = []
        row_threshold = 20  # pixels

        for item in valid_data:
            if not current_row:
                current_row.append(item)
            else:
                # Check if this item is on the same row as the current row
                avg_top = sum(x['top'] for x in current_row) / len(current_row)
                if abs(item['top'] - avg_top) <= row_threshold:
                    current_row.append(item)
                else:
                    # Start new row
                    if current_row:
                        rows.append(current_row)
                    current_row = [item]

        if current_row:
            rows.append(current_row)

        # Sort each row by horizontal position and enhance mathematical content
        table_data = []
        for row in rows:
            row.sort(key=lambda x: x['left'])
            row_texts = []

            for item in row:
                text = item['text']

                # If this looks like mathematical content and we have LaTeX OCR available
                if is_mathematical_content(text) and LATEX_OCR_AVAILABLE and original_img:
                    try:
                        # Extract the region of this cell from the original image
                        cell_img = original_img.crop((
                            max(0, item['left'] - 5), max(0, item['top'] - 5),
                            min(original_img.width, item['right'] + 5),
                            min(original_img.height, item['bottom'] + 5)
                        ))

                        # Try LaTeX OCR on this cell
                        latex_result = extract_math_with_latex_ocr(cell_img)
                        if latex_result and latex_result.strip():
                            text = f"${latex_result}$"  # Wrap in LaTeX math delimiters
                            print(f"  - Enhanced math cell: {text}")
                    except Exception as e:
                        print(f"  - LaTeX OCR failed for cell, using regular OCR: {e}")

                row_texts.append(text)

            if len(row_texts) > 1:  # Only keep rows with multiple columns
                table_data.append(row_texts)

        return table_data if len(table_data) > 1 else None
    except Exception as e:
        print(f"Table extraction error: {e}")
        return None

def parse_text_to_table(text):
    """Try to parse plain text into table format"""
    lines = [line.strip() for line in text.splitlines() if line.strip()]
    if not lines:
        return None

    # Look for common table patterns
    table_data = []

    for line in lines:
        # Try to split by multiple spaces, tabs, or common separators
        # First try splitting by multiple spaces (2 or more)
        cells = re.split(r'\s{2,}|\t', line)

        # If that doesn't work well, try other separators
        if len(cells) < 2:
            cells = re.split(r'\s+', line)

        # Clean up cells
        cells = [cell.strip() for cell in cells if cell.strip()]

        if len(cells) > 1:  # Only add rows with multiple columns
            table_data.append(cells)

    return table_data if len(table_data) > 1 else None

def create_word_table(doc, table_data):
    """Create a Word table from table data with equation support"""
    if not table_data:
        return

    # Determine table dimensions
    max_cols = max(len(row) for row in table_data)
    rows = len(table_data)

    # Create table
    table = doc.add_table(rows=rows, cols=max_cols)
    table.style = 'Table Grid'
    table.alignment = WD_TABLE_ALIGNMENT.CENTER

    # Fill table with smart equation handling
    for i, row_data in enumerate(table_data):
        row = table.rows[i]
        for j, cell_data in enumerate(row_data):
            if j < len(row.cells):
                cell = row.cells[j]
                cell_text = str(cell_data)

                # Check if this cell contains a mathematical expression
                if cell_text.startswith('$') and cell_text.endswith('$'):
                    # This is a LaTeX expression - insert as equation
                    equation_text = cell_text.strip('$')
                    insert_equation_in_cell(cell, equation_text)
                elif is_mathematical_content(cell_text):
                    # This looks like math but isn't LaTeX formatted
                    # Convert to Linear format and insert as equation
                    linear_eq = latex_to_linear(cell_text)
                    insert_equation_in_cell(cell, linear_eq)
                else:
                    # Regular text
                    cell.text = cell_text

    # Make first row bold (assuming it's header)
    if table.rows:
        for cell in table.rows[0].cells:
            # Only make non-equation cells bold
            if not (cell.text.startswith('$') or is_mathematical_content(cell.text)):
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        run.bold = True

def process_image_to_docx(image_path, output_path):
    try:
        img = Image.open(image_path)
        
        # Preprocess image for better OCR
        img_processed = preprocess_image_for_table(img)
        
        # Try multiple OCR configurations for better table detection
        configs = [
            '--psm 6 -c preserve_interword_spaces=1',  # Single uniform block
            '--psm 4 -c preserve_interword_spaces=1',  # Single column of text
            '--psm 3 -c preserve_interword_spaces=1',  # Fully automatic page segmentation
        ]
        
        best_table_data = None
        best_text = ""
        
        for config in configs:
            try:
                text = pytesseract.image_to_string(img_processed, config=config)
                data = pytesseract.image_to_data(img_processed, output_type=pytesseract.Output.DICT, config=config)
                table_data = extract_table_from_ocr_data(data, img_processed)
                
                if table_data and len(table_data) > 1:
                    # Check if this result looks better (more columns, more structured)
                    avg_cols = sum(len(row) for row in table_data) / len(table_data)
                    if best_table_data is None or avg_cols > sum(len(row) for row in best_table_data) / len(best_table_data):
                        best_table_data = table_data
                        best_text = text
                        break
            except:
                continue
        
        # Use the best result or fallback
        if not best_table_data:
            text = pytesseract.image_to_string(img_processed, config='--psm 6 -c preserve_interword_spaces=1')
            best_text = text
        
        doc = Document()
        doc.add_heading(f'Extracted Table: {os.path.basename(image_path)}', level=1)
        
        # Use the best table data found
        final_table_data = best_table_data
        final_text = best_text if best_table_data else text
        
        if final_table_data and len(final_table_data) > 1:  # If we have table data with multiple rows
            create_word_table(doc, final_table_data)
        else:
            # Fallback: try to parse text into table format
            fallback_table_data = parse_text_to_table(final_text)
            if fallback_table_data and len(fallback_table_data) > 1:
                create_word_table(doc, fallback_table_data)
            else:
                # If no table structure detected, add as paragraphs
                doc.add_paragraph("Raw extracted text:")
                for line in final_text.splitlines():
                    if line.strip():
                        doc.add_paragraph(line)
        
        doc.save(output_path)
        return True
    except Exception as e:
        print(f"Error processing {image_path}: {e}")
        return False

# Test with a specific image
if __name__ == "__main__":
    # Look for common image file extensions
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif']
    image_path = None

    # Find the first image file in the current directory
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            image_path = file
            break

    if image_path:
        output_path = f"enhanced_table_{os.path.splitext(image_path)[0]}.docx"
        print(f"Processing {image_path}...")
        success = process_image_to_docx(image_path, output_path)
        if success:
            print(f"Successfully created {output_path}")
            print("The enhanced version includes:")
            print("- Image preprocessing for better OCR")
            print("- Multiple OCR configurations tested")
            print("- Coordinate-based table structure detection")
            print("- Improved text parsing for mathematical content")
            print("- Proper Word table formatting with headers")
        else:
            print("Failed to process image")
    else:
        print("No image files found in the current directory.")
        print("Please place your table image in this folder and run again.")
        print(f"Supported formats: {', '.join(image_extensions)}")
