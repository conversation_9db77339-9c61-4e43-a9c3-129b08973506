"""
Working OCR Processor - Uses available OCR engines without Tesseract dependency
"""

import os
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
from docx import Document
from docx.shared import Inches, RGBColor
from docx.enum.table import WD_TABLE_ALIGNMENT
import cv2
import datetime

# Check available OCR engines
try:
    import easyocr
    EASYOCR_AVAILABLE = True
    print("✅ EasyOCR available")
except ImportError:
    EASYOCR_AVAILABLE = False
    print("❌ EasyOCR not available")

try:
    from pix2tex.cli import LatexOCR
    LATEX_OCR_AVAILABLE = True
    print("✅ LaTeX OCR available")
except ImportError:
    LATEX_OCR_AVAILABLE = False
    print("❌ LaTeX OCR not available")

class WorkingOCRProcessor:
    """OCR processor that works with available engines"""
    
    def __init__(self):
        print("🚀 Initializing Working OCR Processor...")
        
        # Initialize EasyOCR if available
        self.easyocr_reader = None
        if EASYOCR_AVAILABLE:
            try:
                self.easyocr_reader = easyocr.Reader(['en'])
                print("✅ EasyOCR initialized successfully")
            except Exception as e:
                print(f"❌ EasyOCR initialization failed: {e}")
        
        # Initialize LaTeX OCR if available
        self.latex_model = None
        if LATEX_OCR_AVAILABLE:
            try:
                self.latex_model = LatexOCR()
                print("✅ LaTeX OCR initialized successfully")
            except Exception as e:
                print(f"❌ LaTeX OCR initialization failed: {e}")
        
        # Processing statistics
        self.stats = {
            'total_cells': 0,
            'math_cells': 0,
            'high_confidence_cells': 0,
            'processing_time': 0
        }
    
    def preprocess_image(self, img):
        """Advanced image preprocessing"""
        try:
            print("  🔧 Applying advanced image preprocessing...")
            
            # Convert to OpenCV format
            img_array = np.array(img)
            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array
            
            # Noise reduction
            denoised = cv2.bilateralFilter(gray, 9, 75, 75)
            
            # Contrast enhancement
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
            enhanced = clahe.apply(denoised)
            
            # Table line enhancement
            horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (25, 1))
            vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 25))
            
            horizontal_lines = cv2.morphologyEx(enhanced, cv2.MORPH_OPEN, horizontal_kernel)
            vertical_lines = cv2.morphologyEx(enhanced, cv2.MORPH_OPEN, vertical_kernel)
            
            lines_enhanced = cv2.addWeighted(horizontal_lines, 0.5, vertical_lines, 0.5, 0)
            enhanced = cv2.addWeighted(enhanced, 0.8, lines_enhanced, 0.2, 0)
            
            # Text sharpening
            sharpening_kernel = np.array([[-1,-1,-1], [-1, 9,-1], [-1,-1,-1]])
            sharpened = cv2.filter2D(enhanced, -1, sharpening_kernel)
            
            # Adaptive thresholding
            adaptive_thresh = cv2.adaptiveThreshold(sharpened, 255, 
                                                  cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                                  cv2.THRESH_BINARY, 11, 2)
            
            print("    ✅ Advanced preprocessing completed")
            return Image.fromarray(adaptive_thresh)
            
        except Exception as e:
            print(f"    ❌ Preprocessing failed: {e}")
            return img
    
    def extract_text_with_easyocr(self, img):
        """Extract text using EasyOCR"""
        if not self.easyocr_reader:
            return None
        
        try:
            # Convert PIL to numpy array
            img_array = np.array(img)
            
            results = self.easyocr_reader.readtext(img_array)
            
            # Process results
            text_parts = []
            total_confidence = 0
            for (bbox, text, confidence) in results:
                if confidence > 0.3:  # Filter low confidence
                    text_parts.append(text)
                    total_confidence += confidence
            
            full_text = ' '.join(text_parts)
            avg_confidence = (total_confidence / len(results) * 100) if results else 0
            
            return {
                'text': full_text,
                'confidence': avg_confidence,
                'results': results
            }
        except Exception as e:
            print(f"EasyOCR failed: {e}")
            return None
    
    def is_mathematical_content(self, text):
        """Detect if text contains mathematical expressions"""
        if not text or not text.strip():
            return False
        
        import re
        math_patterns = [
            r'[xy]\s*[+\-=]\s*\d',  # Variables with operations
            r'\d+[xy]',  # Coefficients with variables
            r'[=<>≤≥≠≈]',  # Mathematical operators
            r'\d+/\d+',  # Fractions
            r'[αβγδεζηθικλμνξοπρστυφχψω]',  # Greek letters
        ]
        
        return any(re.search(pattern, text, re.IGNORECASE) for pattern in math_patterns)
    
    def extract_math_with_latex_ocr(self, img):
        """Extract mathematical expressions using LaTeX OCR"""
        if not self.latex_model:
            return None
        
        try:
            latex_result = self.latex_model(img)
            if latex_result and latex_result.strip():
                return latex_result
            return None
        except Exception as e:
            print(f"LaTeX OCR failed: {e}")
            return None
    
    def parse_text_to_table(self, text):
        """Parse OCR text into table format"""
        try:
            lines = [line.strip() for line in text.splitlines() if line.strip()]
            if not lines:
                return None
            
            table_data = []
            
            for line in lines:
                import re
                # Try multiple splitting strategies
                cells = re.split(r'\s{2,}|\t', line)
                
                if len(cells) < 2:
                    cells = re.split(r'\s+', line)
                
                # Clean cells
                cleaned_cells = []
                for cell in cells:
                    cell = cell.strip()
                    if cell:
                        # Process mathematical content
                        if self.is_mathematical_content(cell):
                            self.stats['math_cells'] += 1
                        cleaned_cells.append(cell)
                
                if len(cleaned_cells) > 1:
                    table_data.append(cleaned_cells)
                    self.stats['total_cells'] += len(cleaned_cells)
            
            return table_data if len(table_data) > 1 else None
            
        except Exception as e:
            print(f"Text parsing failed: {e}")
            return None
    
    def create_professional_word_document(self, table_data, image_path, output_path):
        """Create professional Word document"""
        try:
            print("  📄 Creating professional Word document...")
            
            doc = Document()
            
            # Title
            title = doc.add_heading('OCR Table Extraction Results', level=1)
            
            # Source info
            subtitle = doc.add_paragraph()
            subtitle.add_run("Source: ").bold = True
            subtitle.add_run(os.path.basename(image_path))
            
            # Processing statistics
            stats_para = doc.add_paragraph()
            stats_para.add_run("📊 Processing Statistics").bold = True
            stats_para.add_run("\n")
            stats_para.add_run(f"• Total Cells: {self.stats['total_cells']}\n")
            stats_para.add_run(f"• Mathematical Content: {self.stats['math_cells']}\n")
            stats_para.add_run(f"• High Confidence: {self.stats['high_confidence_cells']}\n")
            
            # Timestamp
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            stats_para.add_run(f"• Processing Time: {timestamp}")
            
            if table_data and len(table_data) > 0:
                # Table section
                doc.add_heading('Extracted Table', level=2)
                
                # Create table
                max_cols = max(len(row) for row in table_data)
                table = doc.add_table(rows=len(table_data), cols=max_cols)
                table.style = 'Table Grid'
                table.alignment = WD_TABLE_ALIGNMENT.CENTER
                
                # Fill table data
                for i, row_data in enumerate(table_data):
                    for j, cell_text in enumerate(row_data):
                        if j < max_cols:
                            cell = table.cell(i, j)
                            
                            # Format mathematical content
                            if self.is_mathematical_content(cell_text):
                                cell.text = ""
                                paragraph = cell.paragraphs[0]
                                run = paragraph.add_run(cell_text)
                                run.italic = True
                                run.font.name = 'Cambria Math'
                                try:
                                    run.font.color.rgb = RGBColor(0, 0, 139)
                                except:
                                    pass
                            else:
                                cell.text = cell_text
                
                # Format header row
                if table.rows:
                    for cell in table.rows[0].cells:
                        for paragraph in cell.paragraphs:
                            for run in paragraph.runs:
                                run.bold = True
                
                print(f"    ✅ Created table with {len(table_data)} rows and {max_cols} columns")
            else:
                doc.add_paragraph("No table structure detected in the image.")
            
            # Footer
            footer_para = doc.add_paragraph()
            footer_para.add_run("\n" + "─" * 60 + "\n").font.color.rgb = RGBColor(128, 128, 128)
            footer_para.add_run("Generated by Working OCR Table Extraction System").italic = True
            
            doc.save(output_path)
            print(f"    ✅ Document saved: {output_path}")
            return True
            
        except Exception as e:
            print(f"Document creation failed: {e}")
            return False
    
    def process_image(self, image_path, output_path):
        """Process image with available OCR engines"""
        try:
            print(f"🎯 PROCESSING: {image_path}")
            
            # Reset statistics
            self.stats = {'total_cells': 0, 'math_cells': 0, 'high_confidence_cells': 0}
            
            # Load and preprocess image
            img = Image.open(image_path)
            processed_img = self.preprocess_image(img)
            
            # Extract text using available OCR
            table_data = None
            
            if self.easyocr_reader:
                print("  🔍 Using EasyOCR for text extraction...")
                ocr_result = self.extract_text_with_easyocr(processed_img)
                
                if ocr_result and ocr_result['text']:
                    print(f"    ✅ EasyOCR confidence: {ocr_result['confidence']:.1f}%")
                    if ocr_result['confidence'] > 50:
                        self.stats['high_confidence_cells'] += 1
                    
                    # Parse text to table
                    table_data = self.parse_text_to_table(ocr_result['text'])
            
            if not table_data:
                print("    ❌ No table data extracted")
                return False
            
            # Create Word document
            success = self.create_professional_word_document(table_data, image_path, output_path)
            
            if success:
                print(f"    🎉 SUCCESS: {self.stats['math_cells']} math cells detected")
            
            return success
            
        except Exception as e:
            print(f"Processing failed: {e}")
            return False

def main():
    """Test the working OCR processor"""
    print("=" * 70)
    print("🚀 WORKING OCR TABLE EXTRACTION")
    print("=" * 70)
    
    processor = WorkingOCRProcessor()
    
    # Look for images in current directory
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif']
    image_files = []
    
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            image_files.append(file)
    
    if not image_files:
        print("❌ No image files found in current directory")
        return
    
    print(f"📁 Found {len(image_files)} image file(s):")
    for i, file in enumerate(image_files, 1):
        print(f"   {i}. {file}")
    
    print("\n🔄 Processing images...")
    
    success_count = 0
    for image_file in image_files:
        output_file = f"working_extracted_{os.path.splitext(image_file)[0]}.docx"
        
        if processor.process_image(image_file, output_file):
            success_count += 1
            print(f"✅ Success: {image_file} → {output_file}")
        else:
            print(f"❌ Failed: {image_file}")
    
    print(f"\n🎉 PROCESSING COMPLETE! {success_count}/{len(image_files)} files processed")

if __name__ == "__main__":
    main()
