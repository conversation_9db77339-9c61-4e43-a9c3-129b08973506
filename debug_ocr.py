"""
Debug OCR - Check what text is being extracted
"""

import easyocr
from PIL import Image
import numpy as np

def debug_ocr_extraction():
    """Debug what EasyOCR is extracting"""
    print("🔍 Debugging OCR Extraction...")
    
    # Initialize EasyOCR
    reader = easyocr.Reader(['en'])
    
    # Load test image
    img = Image.open('test_mathematical_table.png')
    img_array = np.array(img)
    
    # Extract text with detailed results
    results = reader.readtext(img_array)
    
    print(f"\n📊 Found {len(results)} text regions:")
    print("-" * 60)
    
    all_text = []
    for i, (bbox, text, confidence) in enumerate(results):
        print(f"{i+1:2d}. Text: '{text}'")
        print(f"    Confidence: {confidence:.3f}")
        print(f"    Bbox: {bbox}")
        print()
        
        if confidence > 0.3:
            all_text.append(text)
    
    print("=" * 60)
    print("COMBINED TEXT:")
    print("=" * 60)
    combined = ' '.join(all_text)
    print(f"'{combined}'")
    
    print("\n" + "=" * 60)
    print("LINE BY LINE:")
    print("=" * 60)
    for line in combined.split('\n'):
        if line.strip():
            print(f"'{line.strip()}'")
    
    # Try different parsing approaches
    print("\n" + "=" * 60)
    print("PARSING ATTEMPTS:")
    print("=" * 60)
    
    # Approach 1: Split by spaces
    words = combined.split()
    print(f"Words: {words}")
    
    # Approach 2: Group by position
    print("\nGrouping by Y-position:")
    
    # Sort results by Y position
    sorted_results = sorted(results, key=lambda x: x[0][0][1])  # Sort by top Y coordinate
    
    current_y = None
    current_row = []
    rows = []
    
    for bbox, text, confidence in sorted_results:
        if confidence > 0.3:
            y_pos = bbox[0][1]  # Top Y coordinate
            
            if current_y is None or abs(y_pos - current_y) < 20:  # Same row (within 20 pixels)
                current_row.append((bbox[0][0], text))  # Store X position and text
                current_y = y_pos
            else:
                # New row
                if current_row:
                    # Sort current row by X position
                    current_row.sort(key=lambda x: x[0])
                    row_texts = [item[1] for item in current_row]
                    rows.append(row_texts)
                    print(f"Row: {row_texts}")
                
                current_row = [(bbox[0][0], text)]
                current_y = y_pos
    
    # Don't forget the last row
    if current_row:
        current_row.sort(key=lambda x: x[0])
        row_texts = [item[1] for item in current_row]
        rows.append(row_texts)
        print(f"Row: {row_texts}")
    
    print(f"\nTotal rows found: {len(rows)}")
    
    return rows

if __name__ == "__main__":
    debug_ocr_extraction()
