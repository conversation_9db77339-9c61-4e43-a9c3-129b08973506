Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: accelerate in c:\users\<USER>\appdata\roaming\python\python38\site-packages (0.27.2)
Requirement already satisfied: protobuf in c:\users\<USER>\appdata\roaming\python\python38\site-packages (5.29.5)
Requirement already satisfied: numpy>=1.17 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from accelerate) (1.24.4)
Requirement already satisfied: packaging>=20.0 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from accelerate) (24.2)
Requirement already satisfied: psutil in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from accelerate) (7.0.0)
Requirement already satisfied: pyyaml in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from accelerate) (6.0.2)
Requirement already satisfied: torch>=1.10.0 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from accelerate) (2.2.0)
Requirement already satisfied: huggingface-hub in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from accelerate) (0.31.1)
Requirement already satisfied: safetensors>=0.3.1 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from accelerate) (0.5.3)
Requirement already satisfied: filelock in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from torch>=1.10.0->accelerate) (3.16.1)
Requirement already satisfied: typing-extensions>=4.8.0 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from torch>=1.10.0->accelerate) (4.13.2)
Requirement already satisfied: sympy in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from torch>=1.10.0->accelerate) (1.12)
Requirement already satisfied: networkx in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from torch>=1.10.0->accelerate) (3.1)
Requirement already satisfied: jinja2 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from torch>=1.10.0->accelerate) (3.1.6)
Requirement already satisfied: fsspec in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from torch>=1.10.0->accelerate) (2024.3.1)
Requirement already satisfied: requests in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from huggingface-hub->accelerate) (2.32.3)
Requirement already satisfied: tqdm>=4.42.1 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from huggingface-hub->accelerate) (4.66.2)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from tqdm>=4.42.1->huggingface-hub->accelerate) (0.4.4)
Requirement already satisfied: MarkupSafe>=2.0 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from jinja2->torch>=1.10.0->accelerate) (2.1.5)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from requests->huggingface-hub->accelerate) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from requests->huggingface-hub->accelerate) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from requests->huggingface-hub->accelerate) (1.26.20)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from requests->huggingface-hub->accelerate) (2025.1.31)
Requirement already satisfied: mpmath>=0.19 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from sympy->torch>=1.10.0->accelerate) (1.3.0)
