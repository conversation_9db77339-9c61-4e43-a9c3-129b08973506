"""
ROBUST OCR PROCESSOR - Handles various image types and quality issues
"""

import os
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
from docx import Document
from docx.shared import RGBColor
from docx.enum.table import WD_TABLE_ALIGNMENT
import cv2
import datetime
import easyocr
import tkinter as tk
from tkinter import filedialog, messagebox

class RobustOCRProcessor:
    """Robust OCR processor that handles various image conditions"""
    
    def __init__(self):
        print("🚀 Initializing Robust OCR Processor...")
        print("   Designed to handle various image types and quality issues")
        
        # Initialize EasyOCR
        self.easyocr_reader = easyocr.Reader(['en'])
        print("✅ EasyOCR initialized successfully")
        
        # Processing statistics
        self.stats = {
            'total_cells': 0,
            'math_cells': 0,
            'high_confidence_cells': 0,
            'processing_time': 0
        }
    
    def analyze_image_quality(self, img):
        """Analyze image quality and suggest best preprocessing"""
        img_array = np.array(img)
        
        # Convert to grayscale for analysis
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        else:
            gray = img_array
        
        # Calculate image statistics
        mean_brightness = np.mean(gray)
        contrast = np.std(gray)
        
        print(f"  📊 Image Analysis:")
        print(f"     Size: {img.size}")
        print(f"     Brightness: {mean_brightness:.1f}/255")
        print(f"     Contrast: {contrast:.1f}")
        
        # Determine best preprocessing approach
        if mean_brightness < 80:
            approach = "dark_image"
            print(f"     🔍 Detected: Dark image - will brighten")
        elif mean_brightness > 200:
            approach = "bright_image"
            print(f"     🔍 Detected: Bright image - will enhance contrast")
        elif contrast < 40:
            approach = "low_contrast"
            print(f"     🔍 Detected: Low contrast - will enhance")
        else:
            approach = "normal"
            print(f"     🔍 Detected: Normal image - standard processing")
        
        return approach
    
    def preprocess_image_adaptive(self, img, approach):
        """Adaptive preprocessing based on image characteristics"""
        try:
            print(f"  🔧 Applying {approach} preprocessing...")
            
            # Convert to OpenCV format
            img_array = np.array(img)
            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array
            
            if approach == "dark_image":
                # Brighten dark images
                brightened = cv2.convertScaleAbs(gray, alpha=1.5, beta=30)
                clahe = cv2.createCLAHE(clipLimit=4.0, tileGridSize=(8,8))
                enhanced = clahe.apply(brightened)
                
            elif approach == "bright_image":
                # Handle bright images with poor contrast
                # Reduce brightness and enhance contrast
                darkened = cv2.convertScaleAbs(gray, alpha=0.8, beta=-20)
                clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
                enhanced = clahe.apply(darkened)
                
            elif approach == "low_contrast":
                # Enhance low contrast images
                clahe = cv2.createCLAHE(clipLimit=4.0, tileGridSize=(6,6))
                enhanced = clahe.apply(gray)
                # Additional contrast enhancement
                enhanced = cv2.convertScaleAbs(enhanced, alpha=1.3, beta=0)
                
            else:  # normal
                # Standard processing
                clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
                enhanced = clahe.apply(gray)
            
            # Common post-processing for all approaches
            # Noise reduction
            denoised = cv2.bilateralFilter(enhanced, 5, 50, 50)
            
            # Sharpening
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            sharpened = cv2.filter2D(denoised, -1, kernel)
            
            # Final thresholding - try adaptive first
            try:
                result = cv2.adaptiveThreshold(sharpened, 255, 
                                             cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                             cv2.THRESH_BINARY, 11, 2)
            except:
                # Fallback to Otsu if adaptive fails
                _, result = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            print("    ✅ Adaptive preprocessing completed")
            return Image.fromarray(result)
            
        except Exception as e:
            print(f"    ⚠️  Preprocessing failed, using original: {e}")
            return img
    
    def extract_text_multiple_attempts(self, img):
        """Try multiple OCR approaches for maximum success"""
        print("  🔍 Attempting OCR with multiple approaches...")
        
        best_results = None
        best_count = 0
        
        # Convert to different formats for OCR
        img_array = np.array(img)
        
        # Attempt 1: Direct OCR on processed image
        try:
            results1 = self.easyocr_reader.readtext(img_array)
            print(f"    Attempt 1: Found {len(results1)} regions")
            if len(results1) > best_count:
                best_results = results1
                best_count = len(results1)
        except Exception as e:
            print(f"    Attempt 1 failed: {e}")
        
        # Attempt 2: Additional morphological processing
        try:
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
            morph = cv2.morphologyEx(img_array, cv2.MORPH_CLOSE, kernel)
            results2 = self.easyocr_reader.readtext(morph)
            print(f"    Attempt 2: Found {len(results2)} regions")
            if len(results2) > best_count:
                best_results = results2
                best_count = len(results2)
        except Exception as e:
            print(f"    Attempt 2 failed: {e}")
        
        # Attempt 3: Erosion/Dilation
        try:
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 1))
            eroded = cv2.erode(img_array, kernel, iterations=1)
            dilated = cv2.dilate(eroded, kernel, iterations=1)
            results3 = self.easyocr_reader.readtext(dilated)
            print(f"    Attempt 3: Found {len(results3)} regions")
            if len(results3) > best_count:
                best_results = results3
                best_count = len(results3)
        except Exception as e:
            print(f"    Attempt 3 failed: {e}")
        
        # Attempt 4: Scale up image (sometimes helps with small text)
        try:
            if img.size[0] < 1000 or img.size[1] < 1000:
                scale_factor = 2
                new_size = (img.size[0] * scale_factor, img.size[1] * scale_factor)
                scaled_img = img.resize(new_size, Image.LANCZOS)
                scaled_array = np.array(scaled_img)
                results4 = self.easyocr_reader.readtext(scaled_array)
                print(f"    Attempt 4 (scaled): Found {len(results4)} regions")
                if len(results4) > best_count:
                    best_results = results4
                    best_count = len(results4)
        except Exception as e:
            print(f"    Attempt 4 failed: {e}")
        
        if best_results:
            print(f"    ✅ Best result: {best_count} text regions found")
            return best_results
        else:
            print(f"    ❌ No text detected with any approach")
            return []
    
    def organize_into_table(self, results):
        """Organize OCR results into table structure with error handling"""
        if not results:
            print("    ❌ No OCR results to organize")
            return None
        
        # Filter by confidence
        filtered_results = []
        for bbox, text, confidence in results:
            if confidence > 0.2:  # Lower threshold for difficult images
                filtered_results.append((bbox, text, confidence))
                if confidence > 0.6:
                    self.stats['high_confidence_cells'] += 1
        
        if not filtered_results:
            print("    ❌ No confident text regions found")
            return None
        
        print(f"    📊 Using {len(filtered_results)} confident regions")
        
        # Sort by Y position (rows)
        sorted_results = sorted(filtered_results, key=lambda x: x[0][0][1])
        
        # Group into rows with larger tolerance for difficult images
        rows = []
        current_row = []
        current_y = None
        row_tolerance = 30  # Increased tolerance
        
        for bbox, text, confidence in sorted_results:
            y_pos = bbox[0][1]
            x_pos = bbox[0][0]
            
            if current_y is None or abs(y_pos - current_y) < row_tolerance:
                current_row.append((x_pos, text, confidence))
                if current_y is None:
                    current_y = y_pos
            else:
                if current_row:
                    current_row.sort(key=lambda x: x[0])  # Sort by X position
                    rows.append(current_row)
                current_row = [(x_pos, text, confidence)]
                current_y = y_pos
        
        if current_row:
            current_row.sort(key=lambda x: x[0])
            rows.append(current_row)
        
        # Convert to clean table format
        table_data = []
        max_cols = 0
        
        for row in rows:
            row_texts = []
            for x_pos, text, confidence in row:
                cleaned_text = self.clean_ocr_text(text)
                row_texts.append(cleaned_text)
                
                if self.is_mathematical_content(cleaned_text):
                    self.stats['math_cells'] += 1
            
            if row_texts:
                table_data.append(row_texts)
                max_cols = max(max_cols, len(row_texts))
                self.stats['total_cells'] += len(row_texts)
        
        # Ensure consistent column count
        for row in table_data:
            while len(row) < max_cols:
                row.append("")
        
        if table_data:
            print(f"    ✅ Created table: {len(table_data)} rows × {max_cols} columns")
            return table_data
        else:
            print(f"    ❌ Could not create table structure")
            return None
    
    def clean_ocr_text(self, text):
        """Clean up OCR text with extensive error correction"""
        if not text:
            return text
        
        cleaned = text.strip()
        
        # Extensive OCR error corrections
        replacements = {
            # Mathematical expressions
            'X-y=1': 'x - y = 1', 'X+': 'x +', 'Y+': 'y +',
            'x-y=1': 'x - y = 1', 'x+2y=1': 'x + 2y = 1',
            
            # Common OCR errors
            '0': ['O', 'o', 'Q'], '1': ['l', 'I', '|'], '2': ['Z'],
            '5': ['S'], '6': ['G'], '8': ['B'], '9': ['g'],
            
            # Table-specific corrections
            'aflaar': 'a₁/a₂', 'bElbz': 'b₁/b₂', 'cElc-': 'c₁/c₂',
            'Ijnes': 'lines', 'lihes': 'lines', 'Iines': 'lines',
            'mary solut': 'many solutions', 'solut': 'solution',
            'represe': 'representation', 'representat': 'representation',
            'Infinitely mary solut': 'Infinitely many solutions',
            'Coincident lihes': 'Coincident lines',
            'Intersecting Ijnes': 'Intersecting lines',
            'Parallel Iines': 'Parallel lines',
            'Graphical represe': 'Graphical representation'
        }
        
        for error, correction in replacements.items():
            if isinstance(correction, list):
                # Multiple possible corrections
                for corr in correction:
                    cleaned = cleaned.replace(corr, error)
            else:
                cleaned = cleaned.replace(error, correction)
        
        return cleaned
    
    def is_mathematical_content(self, text):
        """Detect mathematical expressions with broader patterns"""
        if not text:
            return False
        
        import re
        patterns = [
            r'[xy]\s*[+\-=]\s*\d',  # Variables with operations
            r'\d+[xy]',  # Coefficients with variables
            r'[=<>≤≥≠≈]',  # Mathematical operators
            r'\d+/\d+',  # Fractions
            r'[₁₂₃₄₅₆₇₈₉₀]',  # Subscripts
            r'[+\-]\s*\d+[xy]',  # Terms like +2x, -3y
            r'[xy]\s*[+\-]\s*[xy]',  # Variable combinations
        ]
        
        return any(re.search(pattern, text, re.IGNORECASE) for pattern in patterns)
    
    def create_word_document(self, table_data, image_path, output_path):
        """Create Word document with enhanced formatting"""
        try:
            print("  📄 Creating Word document...")
            
            doc = Document()
            
            # Title and metadata
            doc.add_heading('Robust OCR Table Extraction', level=1)
            
            info_para = doc.add_paragraph()
            info_para.add_run("Source Image: ").bold = True
            info_para.add_run(os.path.basename(image_path))
            info_para.add_run(f"\nProcessed: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Statistics
            stats_para = doc.add_paragraph()
            stats_para.add_run("\n📊 Extraction Statistics").bold = True
            stats_para.add_run(f"\n• Total Cells: {self.stats['total_cells']}")
            stats_para.add_run(f"\n• Mathematical Expressions: {self.stats['math_cells']}")
            stats_para.add_run(f"\n• High Confidence Results: {self.stats['high_confidence_cells']}")
            
            if table_data:
                # Create table
                doc.add_heading('Extracted Table', level=2)
                
                rows = len(table_data)
                cols = max(len(row) for row in table_data) if table_data else 0
                
                table = doc.add_table(rows=rows, cols=cols)
                table.style = 'Table Grid'
                table.alignment = WD_TABLE_ALIGNMENT.CENTER
                
                # Fill table
                for i, row_data in enumerate(table_data):
                    for j, cell_text in enumerate(row_data):
                        if j < cols:
                            cell = table.cell(i, j)
                            
                            if self.is_mathematical_content(cell_text):
                                # Format mathematical content
                                cell.text = ""
                                para = cell.paragraphs[0]
                                run = para.add_run(cell_text)
                                run.italic = True
                                run.font.name = 'Cambria Math'
                                run.font.color.rgb = RGBColor(0, 0, 139)
                            else:
                                cell.text = cell_text
                
                # Bold header row
                if table.rows:
                    for cell in table.rows[0].cells:
                        for para in cell.paragraphs:
                            for run in para.runs:
                                run.bold = True
                
                print(f"    ✅ Table created: {rows} rows × {cols} columns")
            else:
                doc.add_paragraph("❌ No table structure could be detected in this image.")
                doc.add_paragraph("Suggestions:")
                doc.add_paragraph("• Ensure the image has good contrast and resolution")
                doc.add_paragraph("• Try cropping to focus on the table area")
                doc.add_paragraph("• Check that text is clearly visible")
            
            # Footer
            doc.add_paragraph("\n" + "─" * 50)
            footer = doc.add_paragraph("Generated by Robust OCR Table Extraction System")
            footer.runs[0].italic = True
            
            doc.save(output_path)
            print(f"    ✅ Document saved: {output_path}")
            return True
            
        except Exception as e:
            print(f"    ❌ Document creation failed: {e}")
            return False
    
    def process_image(self, image_path, output_path):
        """Process image with robust error handling"""
        try:
            print(f"\n🎯 ROBUST PROCESSING: {os.path.basename(image_path)}")
            
            # Reset stats
            self.stats = {'total_cells': 0, 'math_cells': 0, 'high_confidence_cells': 0}
            
            # Load image
            img = Image.open(image_path)
            
            # Analyze image quality
            approach = self.analyze_image_quality(img)
            
            # Adaptive preprocessing
            processed_img = self.preprocess_image_adaptive(img, approach)
            
            # Multiple OCR attempts
            ocr_results = self.extract_text_multiple_attempts(processed_img)
            
            # Organize into table
            table_data = self.organize_into_table(ocr_results)
            
            # Create document (even if no table found)
            success = self.create_word_document(table_data, image_path, output_path)
            
            if table_data:
                print(f"    🎉 SUCCESS! Math cells: {self.stats['math_cells']}, High confidence: {self.stats['high_confidence_cells']}")
            else:
                print(f"    ⚠️  Completed with no table data - check document for suggestions")
            
            return success
            
        except Exception as e:
            print(f"    ❌ Processing failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Main application"""
    print("=" * 70)
    print("🎯 ROBUST OCR TABLE EXTRACTION SYSTEM")
    print("=" * 70)
    print("Handles various image types and quality issues!")
    print("=" * 70)
    
    # Initialize processor
    processor = RobustOCRProcessor()
    
    # GUI file selection
    root = tk.Tk()
    root.withdraw()
    
    # Select input files
    print("\n📁 Select your image files...")
    file_paths = filedialog.askopenfilenames(
        title="Select Image Files",
        filetypes=[
            ("Image files", "*.png *.jpg *.jpeg *.bmp *.tiff *.gif"),
            ("All files", "*.*")
        ]
    )
    
    if not file_paths:
        print("❌ No files selected. Exiting.")
        return
    
    # Select output directory
    print("📁 Select output directory...")
    output_dir = filedialog.askdirectory(title="Select Output Directory")
    
    if not output_dir:
        print("❌ No output directory selected. Exiting.")
        return
    
    print(f"\n🔄 Processing {len(file_paths)} file(s)...")
    
    # Process files
    success_count = 0
    for i, file_path in enumerate(file_paths, 1):
        print(f"\n[{i}/{len(file_paths)}]", end=" ")
        
        filename = os.path.splitext(os.path.basename(file_path))[0]
        output_path = os.path.join(output_dir, f"{filename}_robust_extracted.docx")
        
        if processor.process_image(file_path, output_path):
            success_count += 1
        
    # Summary
    print("\n" + "=" * 70)
    print("🎉 ROBUST PROCESSING COMPLETE!")
    print(f"✅ Successfully processed: {success_count}/{len(file_paths)} files")
    print(f"📁 Output location: {output_dir}")
    print("=" * 70)
    
    # Show completion message
    messagebox.showinfo(
        "Processing Complete",
        f"Robust processing completed!\n\n"
        f"Processed: {success_count}/{len(file_paths)} files\n"
        f"Output saved to: {output_dir}\n\n"
        f"Note: Even if no table was detected, documents were created with suggestions."
    )

if __name__ == "__main__":
    main()
