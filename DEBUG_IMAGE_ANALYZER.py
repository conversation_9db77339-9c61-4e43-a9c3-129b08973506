"""
Debug Image Analyzer - Analyze why OCR is not detecting text
"""

import os
import numpy as np
from PIL import Image, ImageEnhance
import cv2
import easyocr
# import matplotlib.pyplot as plt  # Not needed for basic analysis

def analyze_image(image_path):
    """Comprehensive image analysis for OCR troubleshooting"""
    print("=" * 70)
    print(f"🔍 ANALYZING IMAGE: {os.path.basename(image_path)}")
    print("=" * 70)
    
    try:
        # Load image
        img = Image.open(image_path)
        print(f"📊 Image Info:")
        print(f"   Size: {img.size}")
        print(f"   Mode: {img.mode}")
        print(f"   Format: {img.format}")
        
        # Convert to array for analysis
        img_array = np.array(img)
        print(f"   Array shape: {img_array.shape}")
        
        # Check if image is too dark/light
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        else:
            gray = img_array
        
        mean_brightness = np.mean(gray)
        print(f"   Mean brightness: {mean_brightness:.1f} (0=black, 255=white)")
        
        # Check contrast
        contrast = np.std(gray)
        print(f"   Contrast (std dev): {contrast:.1f}")
        
        # Analyze image quality
        if mean_brightness < 50:
            print("   ⚠️  Image is very dark - may need brightness adjustment")
        elif mean_brightness > 200:
            print("   ⚠️  Image is very bright - may need contrast adjustment")
        else:
            print("   ✅ Brightness looks good")
        
        if contrast < 30:
            print("   ⚠️  Low contrast - text may be hard to detect")
        else:
            print("   ✅ Contrast looks good")
        
        # Try different preprocessing approaches
        print(f"\n🔧 Testing different preprocessing approaches...")
        
        # Approach 1: Basic grayscale
        print("   1. Basic grayscale conversion...")
        test_basic_ocr(gray, "Basic Grayscale")
        
        # Approach 2: Enhanced contrast
        print("   2. Enhanced contrast...")
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        test_basic_ocr(enhanced, "Enhanced Contrast")
        
        # Approach 3: Adaptive threshold
        print("   3. Adaptive threshold...")
        adaptive = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
        test_basic_ocr(adaptive, "Adaptive Threshold")
        
        # Approach 4: Otsu threshold
        print("   4. Otsu threshold...")
        _, otsu = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        test_basic_ocr(otsu, "Otsu Threshold")
        
        # Approach 5: Morphological operations
        print("   5. Morphological enhancement...")
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        morph = cv2.morphologyEx(enhanced, cv2.MORPH_CLOSE, kernel)
        test_basic_ocr(morph, "Morphological")
        
        # Save processed versions for manual inspection
        save_debug_images(image_path, gray, enhanced, adaptive, otsu, morph)
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()

def test_basic_ocr(img_array, method_name):
    """Test OCR on processed image"""
    try:
        # Initialize EasyOCR (reuse if already initialized)
        if not hasattr(test_basic_ocr, 'reader'):
            print("      Initializing EasyOCR...")
            test_basic_ocr.reader = easyocr.Reader(['en'])
        
        # Run OCR
        results = test_basic_ocr.reader.readtext(img_array)
        
        print(f"      {method_name}: Found {len(results)} text regions")
        
        if results:
            high_conf_count = sum(1 for _, _, conf in results if conf > 0.3)
            print(f"         High confidence (>30%): {high_conf_count}")
            
            # Show top 3 results
            sorted_results = sorted(results, key=lambda x: x[2], reverse=True)
            for i, (bbox, text, conf) in enumerate(sorted_results[:3]):
                print(f"         {i+1}. '{text}' (confidence: {conf:.3f})")
        
    except Exception as e:
        print(f"      {method_name}: OCR failed - {e}")

def save_debug_images(original_path, gray, enhanced, adaptive, otsu, morph):
    """Save processed images for manual inspection"""
    try:
        base_name = os.path.splitext(os.path.basename(original_path))[0]
        
        # Save processed versions
        cv2.imwrite(f"debug_{base_name}_1_gray.png", gray)
        cv2.imwrite(f"debug_{base_name}_2_enhanced.png", enhanced)
        cv2.imwrite(f"debug_{base_name}_3_adaptive.png", adaptive)
        cv2.imwrite(f"debug_{base_name}_4_otsu.png", otsu)
        cv2.imwrite(f"debug_{base_name}_5_morph.png", morph)
        
        print(f"\n💾 Saved debug images:")
        print(f"   debug_{base_name}_1_gray.png")
        print(f"   debug_{base_name}_2_enhanced.png")
        print(f"   debug_{base_name}_3_adaptive.png")
        print(f"   debug_{base_name}_4_otsu.png")
        print(f"   debug_{base_name}_5_morph.png")
        print(f"   👀 Check these images to see which preprocessing works best")
        
    except Exception as e:
        print(f"Failed to save debug images: {e}")

def suggest_solutions(image_path):
    """Suggest solutions based on analysis"""
    print(f"\n💡 SUGGESTED SOLUTIONS:")
    print(f"=" * 50)
    
    print(f"1. 📸 Image Quality:")
    print(f"   • Ensure image is high resolution (300+ DPI recommended)")
    print(f"   • Check that text is clearly visible to human eye")
    print(f"   • Avoid blurry or low-contrast images")
    
    print(f"\n2. 🔧 Preprocessing:")
    print(f"   • Try the debug images generated above")
    print(f"   • Use the version that makes text most visible")
    print(f"   • Consider manual image editing if needed")
    
    print(f"\n3. 🎯 OCR Settings:")
    print(f"   • Try different OCR engines (Tesseract, PaddleOCR)")
    print(f"   • Adjust confidence thresholds")
    print(f"   • Use different page segmentation modes")
    
    print(f"\n4. 📋 Alternative Approaches:")
    print(f"   • Crop image to focus on table area only")
    print(f"   • Increase image size/resolution")
    print(f"   • Adjust lighting and contrast manually")

def main():
    """Main debug function"""
    # Look for images in current directory
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif']
    image_files = []
    
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            image_files.append(file)
    
    if not image_files:
        print("❌ No image files found in current directory")
        return
    
    print("📁 Found image files:")
    for i, file in enumerate(image_files, 1):
        print(f"   {i}. {file}")
    
    # Analyze each image
    for image_file in image_files:
        analyze_image(image_file)
        suggest_solutions(image_file)
        print("\n" + "=" * 70)

if __name__ == "__main__":
    main()
