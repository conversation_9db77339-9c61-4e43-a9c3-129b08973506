from collections.abc import Mapping, Sequence
from os import PathLike

def escape_path(word: str) -> str: ...

class Writer:
    output: str
    width: int

    def __init__(self, output: str, width: int = ...): ...
    def newline(self) -> None: ...
    def comment(self, text: str) -> None: ...
    def variable(self, key: str, value: list[str] | str, indent: int = ...) -> None: ...
    def pool(self, name: str, depth: int) -> None: ...
    def rule(self, name: str, command: str, description: str | None = None,
             depfile: str | None = None, generator: bool = False,
             pool: str | None = None, restat: bool = False,
             rspfile: str | None = None, rspfile_content: str | None = None,
             deps: str | None = None) -> None:
        ...

    def build(self, outputs: list[str], rule: str, inputs: list[str] | None = None,
              implicit: list[str] | None = None, order_only: list[str] | None = None,
              variables: dict[str, str] | None = None,
              implicit_outputs: list[str] | None = None,
              pool: str | None = None, dyndep: str | None = None) -> None:
        ...
    def include(self, path: str | PathLike[str]) -> None: ...
    def subninja(self, path: str | PathLike[str]) -> None: ...
    def default(self, paths: Sequence[str | PathLike[str]]) -> None: ...
    def close(self) -> None: ...

def as_list(input: None | list[str] | str) -> list[str]: ...

def escape(string: str) -> str: ...

def expand(string: str, vars: Mapping[str, str], local_vars: Mapping[str, str]=...) -> str: ...
