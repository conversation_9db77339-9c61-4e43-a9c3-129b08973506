"""
Advanced equation utilities for Word document creation
"""

from docx.oxml import OxmlElement
from docx.oxml.ns import qn
from docx.shared import RGBColor
import re

def create_equation_element(linear_text):
    """Create a proper Word equation element using OMML"""
    try:
        # Create the math element
        math_element = OxmlElement('m:oMath')
        math_element.set(qn('xmlns:m'), 'http://schemas.openxmlformats.org/officeDocument/2006/math')
        
        # Create run element
        run_element = OxmlElement('m:r')
        
        # Create text element
        text_element = OxmlElement('m:t')
        text_element.text = linear_text
        
        # Assemble the structure
        run_element.append(text_element)
        math_element.append(run_element)
        
        return math_element
    except Exception as e:
        print(f"Failed to create equation element: {e}")
        return None

def insert_equation_advanced(cell, equation_text):
    """Insert equation using advanced OMML approach"""
    try:
        # Convert LaTeX to Linear format
        linear_eq = latex_to_linear(equation_text)
        
        # Clear the cell
        cell.text = ""
        paragraph = cell.paragraphs[0]
        
        # Try to create proper equation element
        equation_element = create_equation_element(linear_eq)
        
        if equation_element is not None:
            # Insert the equation element
            paragraph._element.append(equation_element)
            print(f"  🧮 Inserted OMML equation: {linear_eq}")
            return True
        else:
            # Fallback to formatted text
            return insert_formatted_math_text(cell, linear_eq)
            
    except Exception as e:
        print(f"Advanced equation insertion failed: {e}")
        return insert_formatted_math_text(cell, equation_text)

def insert_formatted_math_text(cell, equation_text):
    """Insert mathematically formatted text as fallback"""
    try:
        # Convert LaTeX to Linear format if needed
        if equation_text.startswith('\\') or '{' in equation_text:
            linear_eq = latex_to_linear(equation_text)
        else:
            linear_eq = equation_text
        
        # Clear the cell and add formatted mathematical text
        cell.text = ""
        paragraph = cell.paragraphs[0]
        
        # Add the equation with special formatting
        run = paragraph.add_run(linear_eq)
        run.italic = True
        run.font.name = 'Cambria Math'  # Use math font
        run.font.size = None  # Use default size
        
        # Add color to distinguish equations
        try:
            run.font.color.rgb = RGBColor(0, 0, 139)  # Dark blue for equations
        except:
            pass
        
        print(f"  📝 Inserted formatted math text: {linear_eq}")
        return True
            
    except Exception as e:
        # Final fallback: plain text
        cell.text = equation_text
        print(f"  ⚠️  Inserted as plain text: {equation_text} (Error: {e})")
        return False

def latex_to_linear(latex_expr):
    """Convert LaTeX expression to Word Linear equation format"""
    if not latex_expr:
        return latex_expr
    
    # Remove LaTeX delimiters if present
    latex_expr = latex_expr.strip('$')
    
    # Enhanced LaTeX to Linear conversions
    conversions = {
        # Fractions - improved handling
        r'\\frac\{([^}]+)\}\{([^}]+)\}': r'(\1)/(\2)',
        r'(\d+)/(\d+)': r'(\1)/(\2)',
        r'([a-zA-Z]+)/([a-zA-Z]+)': r'(\1)/(\2)',
        
        # Superscripts and subscripts - improved
        r'\^(\d+)': r'^(\1)',
        r'_(\d+)': r'_(\1)',
        r'\^{([^}]+)}': r'^(\1)',
        r'_{([^}]+)}': r'_(\1)',
        r'([a-zA-Z0-9])\^([a-zA-Z0-9])': r'\1^(\2)',
        r'([a-zA-Z0-9])_([a-zA-Z0-9])': r'\1_(\2)',
        
        # Greek letters - comprehensive
        r'\\alpha': 'α',
        r'\\beta': 'β',
        r'\\gamma': 'γ',
        r'\\delta': 'δ',
        r'\\epsilon': 'ε',
        r'\\zeta': 'ζ',
        r'\\eta': 'η',
        r'\\theta': 'θ',
        r'\\iota': 'ι',
        r'\\kappa': 'κ',
        r'\\lambda': 'λ',
        r'\\mu': 'μ',
        r'\\nu': 'ν',
        r'\\xi': 'ξ',
        r'\\omicron': 'ο',
        r'\\pi': 'π',
        r'\\rho': 'ρ',
        r'\\sigma': 'σ',
        r'\\tau': 'τ',
        r'\\upsilon': 'υ',
        r'\\phi': 'φ',
        r'\\chi': 'χ',
        r'\\psi': 'ψ',
        r'\\omega': 'ω',
        
        # Capital Greek letters
        r'\\Gamma': 'Γ',
        r'\\Delta': 'Δ',
        r'\\Theta': 'Θ',
        r'\\Lambda': 'Λ',
        r'\\Xi': 'Ξ',
        r'\\Pi': 'Π',
        r'\\Sigma': 'Σ',
        r'\\Upsilon': 'Υ',
        r'\\Phi': 'Φ',
        r'\\Psi': 'Ψ',
        r'\\Omega': 'Ω',
        
        # Mathematical operators - comprehensive
        r'\\leq': '≤',
        r'\\geq': '≥',
        r'\\neq': '≠',
        r'\\approx': '≈',
        r'\\equiv': '≡',
        r'\\pm': '±',
        r'\\mp': '∓',
        r'\\times': '×',
        r'\\div': '÷',
        r'\\cdot': '·',
        r'\\bullet': '•',
        r'\\cap': '∩',
        r'\\cup': '∪',
        r'\\subset': '⊂',
        r'\\supset': '⊃',
        r'\\subseteq': '⊆',
        r'\\supseteq': '⊇',
        r'\\in': '∈',
        r'\\notin': '∉',
        r'\\exists': '∃',
        r'\\forall': '∀',
        r'\\partial': '∂',
        r'\\nabla': '∇',
        r'\\infty': '∞',
        
        # Integrals and sums
        r'\\int': '∫',
        r'\\iint': '∬',
        r'\\iiint': '∭',
        r'\\oint': '∮',
        r'\\sum': '∑',
        r'\\prod': '∏',
        r'\\coprod': '∐',
        
        # Square roots and radicals
        r'\\sqrt\{([^}]+)\}': r'√(\1)',
        r'\\sqrt\[(\d+)\]\{([^}]+)\}': r'(\2)^(1/\1)',
        
        # Trigonometric functions
        r'\\sin': 'sin',
        r'\\cos': 'cos',
        r'\\tan': 'tan',
        r'\\sec': 'sec',
        r'\\csc': 'csc',
        r'\\cot': 'cot',
        r'\\arcsin': 'arcsin',
        r'\\arccos': 'arccos',
        r'\\arctan': 'arctan',
        
        # Logarithms
        r'\\log': 'log',
        r'\\ln': 'ln',
        r'\\lg': 'lg',
        
        # Limits
        r'\\lim': 'lim',
        r'\\max': 'max',
        r'\\min': 'min',
        r'\\sup': 'sup',
        r'\\inf': 'inf',
        
        # Arrows
        r'\\rightarrow': '→',
        r'\\leftarrow': '←',
        r'\\leftrightarrow': '↔',
        r'\\Rightarrow': '⇒',
        r'\\Leftarrow': '⇐',
        r'\\Leftrightarrow': '⇔',
        
        # Remove remaining LaTeX commands
        r'\\([a-zA-Z]+)': r'\1',
        r'\{([^}]+)\}': r'\1',
        r'\\': '',
    }
    
    # Apply conversions in order
    linear_expr = latex_expr
    for pattern, replacement in conversions.items():
        linear_expr = re.sub(pattern, replacement, linear_expr)
    
    # Clean up extra spaces and formatting
    linear_expr = re.sub(r'\s+', ' ', linear_expr).strip()
    
    return linear_expr

def is_mathematical_content(text):
    """Enhanced mathematical content detection"""
    if not text or len(text.strip()) < 2:
        return False
    
    # Enhanced patterns for mathematical content
    math_patterns = [
        r'[xy]\s*[+\-=]\s*\d',  # Variables with operations
        r'\d+[xy]',  # Coefficients with variables
        r'[=<>≤≥≠≈≡]',  # Mathematical operators
        r'\d+/\d+',  # Fractions
        r'[αβγδεζηθικλμνξοπρστυφχψωΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩ]',  # Greek letters
        r'[\^_]\{?[a-zA-Z0-9]+\}?',  # Superscripts/subscripts
        r'\\[a-zA-Z]+',  # LaTeX commands
        r'[∫∬∭∮∑∏∐]',  # Integrals and sums
        r'[√∛∜]',  # Roots
        r'[±∓×÷·•]',  # Mathematical operators
        r'[∞∂∇∃∀]',  # Special math symbols
        r'[⊂⊃⊆⊇∈∉∩∪]',  # Set theory
        r'[→←↔⇒⇐⇔]',  # Arrows
        r'\b(sin|cos|tan|sec|csc|cot|log|ln|lim|max|min|exp)\b',  # Functions
        r'\b[a-zA-Z]\s*[+\-]\s*[a-zA-Z]\s*=',  # Algebraic equations
        r'\d+\s*[a-zA-Z]\s*[+\-=]',  # Coefficient patterns
    ]
    
    # Check if any pattern matches
    for pattern in math_patterns:
        if re.search(pattern, text):
            return True
    
    # Additional heuristics
    # Check for multiple mathematical symbols
    math_symbols = len(re.findall(r'[+\-=<>×÷±∓≤≥≠≈∞∫∑∏√]', text))
    if math_symbols >= 2:
        return True
    
    # Check for variable patterns
    variables = len(re.findall(r'\b[a-zA-Z]\b', text))
    numbers = len(re.findall(r'\d+', text))
    if variables >= 2 and numbers >= 1:
        return True
    
    return False
