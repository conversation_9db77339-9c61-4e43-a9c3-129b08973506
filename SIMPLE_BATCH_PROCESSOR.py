"""
SIMPLE BATCH PROCESSOR - Process all images in current folder
Just place your images in the same folder and run this script
"""

import os
import numpy as np
from PIL import Image
from docx import Document
from docx.shared import RGBColor
from docx.enum.table import WD_TABLE_ALIGNMENT
import cv2
import datetime
import easyocr

def process_all_images_in_folder():
    """Process all images in the current folder"""
    print("=" * 70)
    print("🎯 SIMPLE BATCH OCR PROCESSOR")
    print("=" * 70)
    print("Processing all images in current folder...")
    
    # Initialize EasyOCR
    print("🔄 Initializing OCR engine...")
    reader = easyocr.Reader(['en'])
    print("✅ OCR engine ready!")
    
    # Find all image files
    image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.gif']
    image_files = []
    
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            image_files.append(file)
    
    if not image_files:
        print("❌ No image files found in current folder")
        print("   Supported formats: PNG, JPG, JPEG, BMP, TIFF, GIF")
        input("Press Enter to exit...")
        return
    
    print(f"📁 Found {len(image_files)} image file(s):")
    for i, file in enumerate(image_files, 1):
        print(f"   {i}. {file}")
    
    print(f"\n🔄 Processing {len(image_files)} image(s)...")
    
    success_count = 0
    
    for i, image_file in enumerate(image_files, 1):
        print(f"\n[{i}/{len(image_files)}] Processing: {image_file}")
        
        try:
            # Load image
            img = Image.open(image_file)
            
            # Preprocess
            print("  🔧 Preprocessing...")
            img_array = np.array(img)
            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array
            
            # Enhance image
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)
            
            # OCR extraction
            print("  🔍 Extracting text...")
            results = reader.readtext(enhanced)
            
            if not results:
                print("  ❌ No text found")
                continue
            
            # Organize results by position
            print("  📊 Organizing table structure...")
            
            # Filter by confidence
            filtered_results = [(bbox, text, conf) for bbox, text, conf in results if conf > 0.3]
            
            if not filtered_results:
                print("  ❌ No high-confidence text found")
                continue
            
            # Sort by Y position (rows)
            sorted_results = sorted(filtered_results, key=lambda x: x[0][0][1])
            
            # Group into rows
            rows = []
            current_row = []
            current_y = None
            
            for bbox, text, conf in sorted_results:
                y_pos = bbox[0][1]
                x_pos = bbox[0][0]
                
                if current_y is None or abs(y_pos - current_y) < 25:
                    current_row.append((x_pos, text))
                    if current_y is None:
                        current_y = y_pos
                else:
                    if current_row:
                        current_row.sort(key=lambda x: x[0])  # Sort by X
                        row_texts = [item[1] for item in current_row]
                        rows.append(row_texts)
                    current_row = [(x_pos, text)]
                    current_y = y_pos
            
            if current_row:
                current_row.sort(key=lambda x: x[0])
                row_texts = [item[1] for item in current_row]
                rows.append(row_texts)
            
            if not rows:
                print("  ❌ No table structure found")
                continue
            
            # Clean up text
            cleaned_rows = []
            for row in rows:
                cleaned_row = []
                for text in row:
                    # Basic cleanup
                    cleaned = text.strip()
                    cleaned = cleaned.replace('X-y=1', 'x - y = 1')
                    cleaned = cleaned.replace('X+', 'x +')
                    cleaned = cleaned.replace('Ijnes', 'lines')
                    cleaned = cleaned.replace('lihes', 'lines')
                    cleaned_row.append(cleaned)
                cleaned_rows.append(cleaned_row)
            
            # Make rows consistent length
            if cleaned_rows:
                max_cols = max(len(row) for row in cleaned_rows)
                for row in cleaned_rows:
                    while len(row) < max_cols:
                        row.append("")
            
            # Create Word document
            print("  📄 Creating Word document...")
            
            doc = Document()
            doc.add_heading(f'Extracted Table: {image_file}', level=1)
            
            # Add timestamp
            doc.add_paragraph(f"Processed: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            if cleaned_rows:
                # Create table
                table = doc.add_table(rows=len(cleaned_rows), cols=max_cols)
                table.style = 'Table Grid'
                table.alignment = WD_TABLE_ALIGNMENT.CENTER
                
                # Fill table
                for i, row_data in enumerate(cleaned_rows):
                    for j, cell_text in enumerate(row_data):
                        if j < max_cols:
                            cell = table.cell(i, j)
                            
                            # Check if mathematical content
                            is_math = any(char in cell_text for char in 'xy=+-') and any(char.isdigit() for char in cell_text)
                            
                            if is_math:
                                cell.text = ""
                                para = cell.paragraphs[0]
                                run = para.add_run(cell_text)
                                run.italic = True
                                run.font.name = 'Cambria Math'
                                run.font.color.rgb = RGBColor(0, 0, 139)
                            else:
                                cell.text = cell_text
                
                # Bold first row (header)
                if table.rows:
                    for cell in table.rows[0].cells:
                        for para in cell.paragraphs:
                            for run in para.runs:
                                run.bold = True
                
                print(f"    ✅ Table: {len(cleaned_rows)} rows × {max_cols} columns")
            else:
                doc.add_paragraph("No table structure detected.")
            
            # Save document
            output_filename = f"extracted_{os.path.splitext(image_file)[0]}.docx"
            doc.save(output_filename)
            
            print(f"  ✅ Saved: {output_filename}")
            success_count += 1
            
        except Exception as e:
            print(f"  ❌ Error: {e}")
            continue
    
    # Summary
    print("\n" + "=" * 70)
    print("🎉 BATCH PROCESSING COMPLETE!")
    print(f"✅ Successfully processed: {success_count}/{len(image_files)} files")
    
    if success_count > 0:
        print("\n📄 Generated files:")
        for image_file in image_files:
            output_file = f"extracted_{os.path.splitext(image_file)[0]}.docx"
            if os.path.exists(output_file):
                print(f"   📋 {output_file}")
    
    print("=" * 70)
    input("Press Enter to exit...")

if __name__ == "__main__":
    process_all_images_in_folder()
