"""
Improved OCR Processor - Handles table structure properly
"""

import os
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
from docx import Document
from docx.shared import Inches, RGBColor
from docx.enum.table import WD_TABLE_ALIGNMENT
import cv2
import datetime
import easyocr

class ImprovedOCRProcessor:
    """Improved OCR processor with better table parsing"""
    
    def __init__(self):
        print("🚀 Initializing Improved OCR Processor...")
        
        # Initialize EasyOCR
        self.easyocr_reader = easyocr.Reader(['en'])
        print("✅ EasyOCR initialized successfully")
        
        # Initialize LaTeX OCR if available
        self.latex_model = None
        try:
            from pix2tex.cli import LatexOCR
            self.latex_model = LatexOCR()
            print("✅ LaTeX OCR initialized successfully")
        except ImportError:
            print("❌ LaTeX OCR not available")
        
        # Processing statistics
        self.stats = {
            'total_cells': 0,
            'math_cells': 0,
            'high_confidence_cells': 0,
            'processing_time': 0
        }
    
    def preprocess_image(self, img):
        """Advanced image preprocessing"""
        try:
            print("  🔧 Applying advanced image preprocessing...")
            
            # Convert to OpenCV format
            img_array = np.array(img)
            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array
            
            # Noise reduction
            denoised = cv2.bilateralFilter(gray, 9, 75, 75)
            
            # Contrast enhancement
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
            enhanced = clahe.apply(denoised)
            
            # Text sharpening
            sharpening_kernel = np.array([[-1,-1,-1], [-1, 9,-1], [-1,-1,-1]])
            sharpened = cv2.filter2D(enhanced, -1, sharpening_kernel)
            
            # Adaptive thresholding
            adaptive_thresh = cv2.adaptiveThreshold(sharpened, 255, 
                                                  cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                                  cv2.THRESH_BINARY, 11, 2)
            
            print("    ✅ Advanced preprocessing completed")
            return Image.fromarray(adaptive_thresh)
            
        except Exception as e:
            print(f"    ❌ Preprocessing failed: {e}")
            return img
    
    def extract_table_structure(self, img):
        """Extract table structure using EasyOCR with position-based parsing"""
        try:
            print("  🔍 Extracting table structure with EasyOCR...")
            
            # Convert PIL to numpy array
            img_array = np.array(img)
            
            # Extract text with positions
            results = self.easyocr_reader.readtext(img_array)
            
            print(f"    📊 Found {len(results)} text regions")
            
            # Filter results by confidence
            filtered_results = []
            for bbox, text, confidence in results:
                if confidence > 0.3:  # Only keep confident results
                    filtered_results.append((bbox, text, confidence))
                    if confidence > 0.7:
                        self.stats['high_confidence_cells'] += 1
            
            print(f"    ✅ Kept {len(filtered_results)} high-confidence regions")
            
            # Group results by rows based on Y-position
            rows = self.group_by_rows(filtered_results)
            
            # Convert to table format
            table_data = self.convert_to_table(rows)
            
            return table_data
            
        except Exception as e:
            print(f"Table structure extraction failed: {e}")
            return None
    
    def group_by_rows(self, results):
        """Group OCR results by rows based on Y-position"""
        if not results:
            return []
        
        # Sort by Y position (top coordinate)
        sorted_results = sorted(results, key=lambda x: x[0][0][1])
        
        rows = []
        current_row = []
        current_y = None
        row_tolerance = 25  # pixels tolerance for same row
        
        for bbox, text, confidence in sorted_results:
            y_pos = bbox[0][1]  # Top Y coordinate
            x_pos = bbox[0][0]  # Left X coordinate
            
            if current_y is None or abs(y_pos - current_y) < row_tolerance:
                # Same row
                current_row.append((x_pos, text, confidence))
                if current_y is None:
                    current_y = y_pos
            else:
                # New row
                if current_row:
                    # Sort current row by X position (left to right)
                    current_row.sort(key=lambda x: x[0])
                    rows.append(current_row)
                
                current_row = [(x_pos, text, confidence)]
                current_y = y_pos
        
        # Don't forget the last row
        if current_row:
            current_row.sort(key=lambda x: x[0])
            rows.append(current_row)
        
        print(f"    📋 Organized into {len(rows)} rows")
        return rows
    
    def convert_to_table(self, rows):
        """Convert grouped rows to table format"""
        if not rows:
            return None
        
        table_data = []
        max_cols = 0
        
        for row in rows:
            row_texts = []
            for x_pos, text, confidence in row:
                # Clean up text
                cleaned_text = self.clean_text(text)
                row_texts.append(cleaned_text)
                
                # Check if mathematical content
                if self.is_mathematical_content(cleaned_text):
                    self.stats['math_cells'] += 1
            
            if row_texts:  # Only add non-empty rows
                table_data.append(row_texts)
                max_cols = max(max_cols, len(row_texts))
                self.stats['total_cells'] += len(row_texts)
        
        # Pad rows to have consistent column count
        for row in table_data:
            while len(row) < max_cols:
                row.append("")
        
        print(f"    ✅ Created table: {len(table_data)} rows × {max_cols} columns")
        return table_data
    
    def clean_text(self, text):
        """Clean up OCR text"""
        if not text:
            return text
        
        # Fix common OCR errors
        cleaned = text.strip()
        
        # Fix mathematical expressions
        cleaned = cleaned.replace('X-y=1', 'x - y = 1')
        cleaned = cleaned.replace('X+', 'x +')
        cleaned = cleaned.replace('aflaar', 'a₁/a₂')
        cleaned = cleaned.replace('bElbz', 'b₁/b₂')
        cleaned = cleaned.replace('cElc-', 'c₁/c₂')
        cleaned = cleaned.replace('Ijnes', 'lines')
        cleaned = cleaned.replace('lihes', 'lines')
        cleaned = cleaned.replace('mary solut', 'many solutions')
        cleaned = cleaned.replace('represe', 'representation')
        
        return cleaned
    
    def is_mathematical_content(self, text):
        """Detect if text contains mathematical expressions"""
        if not text or not text.strip():
            return False
        
        import re
        math_patterns = [
            r'[xy]\s*[+\-=]\s*\d',  # Variables with operations
            r'\d+[xy]',  # Coefficients with variables
            r'[=<>≤≥≠≈]',  # Mathematical operators
            r'\d+/\d+',  # Fractions
            r'[₁₂₃₄₅₆₇₈₉₀]',  # Subscripts
        ]
        
        return any(re.search(pattern, text, re.IGNORECASE) for pattern in math_patterns)
    
    def create_professional_word_document(self, table_data, image_path, output_path):
        """Create professional Word document"""
        try:
            print("  📄 Creating professional Word document...")
            
            doc = Document()
            
            # Title
            title = doc.add_heading('OCR Table Extraction Results', level=1)
            
            # Source info
            subtitle = doc.add_paragraph()
            subtitle.add_run("Source: ").bold = True
            subtitle.add_run(os.path.basename(image_path))
            
            # Processing statistics
            stats_para = doc.add_paragraph()
            stats_para.add_run("📊 Processing Statistics").bold = True
            stats_para.add_run("\n")
            stats_para.add_run(f"• Total Cells: {self.stats['total_cells']}\n")
            stats_para.add_run(f"• Mathematical Content: {self.stats['math_cells']}\n")
            stats_para.add_run(f"• High Confidence: {self.stats['high_confidence_cells']}\n")
            
            # Timestamp
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            stats_para.add_run(f"• Processing Time: {timestamp}")
            
            if table_data and len(table_data) > 0:
                # Table section
                doc.add_heading('Extracted Table', level=2)
                
                # Create table
                max_cols = max(len(row) for row in table_data) if table_data else 0
                table = doc.add_table(rows=len(table_data), cols=max_cols)
                table.style = 'Table Grid'
                table.alignment = WD_TABLE_ALIGNMENT.CENTER
                
                # Fill table data
                for i, row_data in enumerate(table_data):
                    for j, cell_text in enumerate(row_data):
                        if j < max_cols:
                            cell = table.cell(i, j)
                            
                            # Format mathematical content
                            if self.is_mathematical_content(cell_text):
                                cell.text = ""
                                paragraph = cell.paragraphs[0]
                                run = paragraph.add_run(cell_text)
                                run.italic = True
                                run.font.name = 'Cambria Math'
                                try:
                                    run.font.color.rgb = RGBColor(0, 0, 139)
                                except:
                                    pass
                            else:
                                cell.text = cell_text
                
                # Format header row
                if table.rows:
                    for cell in table.rows[0].cells:
                        for paragraph in cell.paragraphs:
                            for run in paragraph.runs:
                                run.bold = True
                
                print(f"    ✅ Created table with {len(table_data)} rows and {max_cols} columns")
            else:
                doc.add_paragraph("No table structure detected in the image.")
            
            # Footer
            footer_para = doc.add_paragraph()
            footer_para.add_run("\n" + "─" * 60 + "\n").font.color.rgb = RGBColor(128, 128, 128)
            footer_para.add_run("Generated by Improved OCR Table Extraction System").italic = True
            
            doc.save(output_path)
            print(f"    ✅ Document saved: {output_path}")
            return True
            
        except Exception as e:
            print(f"Document creation failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def process_image(self, image_path, output_path):
        """Process image with improved OCR"""
        try:
            print(f"🎯 PROCESSING: {image_path}")
            
            # Reset statistics
            self.stats = {'total_cells': 0, 'math_cells': 0, 'high_confidence_cells': 0}
            
            # Load and preprocess image
            img = Image.open(image_path)
            processed_img = self.preprocess_image(img)
            
            # Extract table structure
            table_data = self.extract_table_structure(processed_img)
            
            if not table_data:
                print("    ❌ No table data extracted")
                return False
            
            # Create Word document
            success = self.create_professional_word_document(table_data, image_path, output_path)
            
            if success:
                print(f"    🎉 SUCCESS: {self.stats['math_cells']} math cells, {self.stats['high_confidence_cells']} high confidence")
            
            return success
            
        except Exception as e:
            print(f"Processing failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Test the improved OCR processor"""
    print("=" * 70)
    print("🚀 IMPROVED OCR TABLE EXTRACTION")
    print("=" * 70)
    
    processor = ImprovedOCRProcessor()
    
    # Look for images in current directory
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif']
    image_files = []
    
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            image_files.append(file)
    
    if not image_files:
        print("❌ No image files found in current directory")
        return
    
    print(f"📁 Found {len(image_files)} image file(s):")
    for i, file in enumerate(image_files, 1):
        print(f"   {i}. {file}")
    
    print("\n🔄 Processing images...")
    
    success_count = 0
    for image_file in image_files:
        output_file = f"improved_extracted_{os.path.splitext(image_file)[0]}.docx"
        
        if processor.process_image(image_file, output_file):
            success_count += 1
            print(f"✅ Success: {image_file} → {output_file}")
        else:
            print(f"❌ Failed: {image_file}")
    
    print(f"\n🎉 PROCESSING COMPLETE! {success_count}/{len(image_files)} files processed")

if __name__ == "__main__":
    main()
