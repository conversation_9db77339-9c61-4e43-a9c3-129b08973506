Defaulting to user installation because normal site-packages is not writeable
Collecting paddleocr
  Downloading paddleocr-3.1.0-py3-none-any.whl.metadata (22 kB)
Collecting paddlex>=3.1.0 (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading paddlex-3.1.2-py3-none-any.whl.metadata (78 kB)
Requirement already satisfied: PyYAML>=6 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddleocr) (6.0.2)
Requirement already satisfied: typing-extensions>=4.12 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddleocr) (4.13.2)
Requirement already satisfied: chardet in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (5.2.0)
Collecting colorlog (from paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading colorlog-6.9.0-py3-none-any.whl.metadata (10 kB)
Requirement already satisfied: filelock in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (3.16.1)
Requirement already satisfied: huggingface_hub in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (0.31.1)
Requirement already satisfied: numpy>=1.24 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (1.24.4)
Requirement already satisfied: packaging in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (24.2)
Requirement already satisfied: pandas>=1.3 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (2.0.3)
Requirement already satisfied: pillow in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (10.0.0)
Requirement already satisfied: prettytable in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (3.11.0)
Collecting py-cpuinfo (from paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading py_cpuinfo-9.0.0-py3-none-any.whl.metadata (794 bytes)
Requirement already satisfied: pydantic>=2 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (2.10.6)
Requirement already satisfied: requests in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (2.32.3)
Collecting ruamel.yaml (from paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading ruamel.yaml-0.18.14-py3-none-any.whl.metadata (24 kB)
Collecting ujson (from paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading ujson-5.10.0-cp38-cp38-win_amd64.whl.metadata (9.5 kB)
Collecting ftfy (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading ftfy-6.2.3-py3-none-any.whl.metadata (7.8 kB)
Collecting imagesize (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading imagesize-1.4.1-py2.py3-none-any.whl.metadata (1.5 kB)
Collecting langchain>=0.2 (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading langchain-0.2.17-py3-none-any.whl.metadata (7.1 kB)
Collecting langchain-community>=0.2 (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading langchain_community-0.2.19-py3-none-any.whl.metadata (2.7 kB)
Collecting langchain-core (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading langchain_core-0.2.43-py3-none-any.whl.metadata (6.2 kB)
Collecting langchain-openai>=0.1 (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading langchain_openai-0.1.25-py3-none-any.whl.metadata (2.6 kB)
Requirement already satisfied: lxml in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (5.4.0)
Collecting openai>=1.63 (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading openai-1.95.0-py3-none-any.whl.metadata (29 kB)
Collecting opencv-contrib-python==********* (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading opencv_contrib_python-*********-cp37-abi3-win_amd64.whl.metadata (20 kB)
Requirement already satisfied: openpyxl in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (3.1.5)
Collecting premailer (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading premailer-3.10.0-py2.py3-none-any.whl.metadata (15 kB)
Requirement already satisfied: pyclipper in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (1.3.0.post6)
Requirement already satisfied: pypdfium2>=4 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (4.30.1)
Requirement already satisfied: scikit-learn in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (1.3.0)
Requirement already satisfied: shapely in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (2.0.7)
Requirement already satisfied: tokenizers>=0.19 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (0.20.3)
Collecting beautifulsoup4 (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading beautifulsoup4-4.13.4-py3-none-any.whl.metadata (3.8 kB)
Requirement already satisfied: einops in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (0.7.0)
Collecting GPUtil>=1.4 (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading GPUtil-1.4.0.tar.gz (5.5 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: Jinja2 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (3.1.6)
Requirement already satisfied: regex in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (2024.11.6)
Requirement already satisfied: tiktoken in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (0.6.0)
Requirement already satisfied: SQLAlchemy<3,>=1.4 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from langchain>=0.2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (2.0.40)
Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from langchain>=0.2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (3.10.11)
Collecting async-timeout<5.0.0,>=4.0.0 (from langchain>=0.2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading async_timeout-4.0.3-py3-none-any.whl.metadata (4.2 kB)
Collecting langchain-text-splitters<0.3.0,>=0.2.0 (from langchain>=0.2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading langchain_text_splitters-0.2.4-py3-none-any.whl.metadata (2.3 kB)
Collecting langsmith<0.2.0,>=0.1.17 (from langchain>=0.2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading langsmith-0.1.147-py3-none-any.whl.metadata (14 kB)
Collecting tenacity!=8.4.0,<9.0.0,>=8.1.0 (from langchain>=0.2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading tenacity-8.5.0-py3-none-any.whl.metadata (1.2 kB)
Collecting dataclasses-json<0.7,>=0.5.7 (from langchain-community>=0.2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading dataclasses_json-0.6.7-py3-none-any.whl.metadata (25 kB)
Collecting jsonpatch<2.0,>=1.33 (from langchain-core->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading jsonpatch-1.33-py2.py3-none-any.whl.metadata (3.0 kB)
Collecting tiktoken (from paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Using cached tiktoken-0.7.0-cp38-cp38-win_amd64.whl.metadata (6.8 kB)
Requirement already satisfied: anyio<5,>=3.5.0 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from openai>=1.63->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (3.7.1)
Collecting distro<2,>=1.7.0 (from openai>=1.63->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading distro-1.9.0-py3-none-any.whl.metadata (6.8 kB)
Requirement already satisfied: httpx<1,>=0.23.0 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from openai>=1.63->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (0.28.1)
Collecting jiter<1,>=0.4.0 (from openai>=1.63->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading jiter-0.9.1-cp38-cp38-win_amd64.whl.metadata (5.3 kB)
Requirement already satisfied: sniffio in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from openai>=1.63->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (1.3.1)
Requirement already satisfied: tqdm>4 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from openai>=1.63->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (4.66.2)
Requirement already satisfied: python-dateutil>=2.8.2 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from pandas>=1.3->paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (2.9.0.post0)
Requirement already satisfied: pytz>=2020.1 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from pandas>=1.3->paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (2025.2)
Requirement already satisfied: tzdata>=2022.1 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from pandas>=1.3->paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (2025.2)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from pydantic>=2->paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (0.7.0)
Requirement already satisfied: pydantic-core==2.27.2 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from pydantic>=2->paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (2.27.2)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from requests->paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from requests->paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from requests->paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (1.26.20)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from requests->paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (2025.1.31)
Requirement already satisfied: fsspec>=2023.5.0 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from huggingface_hub->paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (2024.3.1)
Collecting soupsieve>1.2 (from beautifulsoup4->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading soupsieve-2.7-py3-none-any.whl.metadata (4.6 kB)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from colorlog->paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (0.4.4)
Requirement already satisfied: wcwidth<0.3.0,>=0.2.12 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from ftfy->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (0.2.13)
Requirement already satisfied: MarkupSafe>=2.0 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from Jinja2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (2.1.5)
Requirement already satisfied: et-xmlfile in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from openpyxl->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (2.0.0)
Collecting cssselect (from premailer->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading cssselect-1.2.0-py2.py3-none-any.whl.metadata (2.2 kB)
Collecting cssutils (from premailer->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading cssutils-2.11.1-py3-none-any.whl.metadata (8.7 kB)
Requirement already satisfied: cachetools in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from premailer->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (5.5.2)
Collecting ruamel.yaml.clib>=0.2.7 (from ruamel.yaml->paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading ruamel.yaml.clib-0.2.8-cp38-cp38-win_amd64.whl.metadata (2.3 kB)
Requirement already satisfied: scipy>=1.5.0 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from scikit-learn->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (1.10.1)
Requirement already satisfied: joblib>=1.1.1 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from scikit-learn->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (1.4.2)
Requirement already satisfied: threadpoolctl>=2.0.0 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from scikit-learn->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (3.5.0)
Requirement already satisfied: aiohappyeyeballs>=2.3.0 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain>=0.2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (2.4.4)
Requirement already satisfied: aiosignal>=1.1.2 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain>=0.2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (1.3.1)
Requirement already satisfied: attrs>=17.3.0 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain>=0.2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (25.3.0)
Requirement already satisfied: frozenlist>=1.1.1 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain>=0.2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (1.5.0)
Requirement already satisfied: multidict<7.0,>=4.5 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain>=0.2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (6.1.0)
Requirement already satisfied: yarl<2.0,>=1.12.0 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain>=0.2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (1.15.2)
Requirement already satisfied: exceptiongroup in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from anyio<5,>=3.5.0->openai>=1.63->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (1.3.0)
Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community>=0.2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading marshmallow-3.22.0-py3-none-any.whl.metadata (7.2 kB)
Requirement already satisfied: typing-inspect<1,>=0.4.0 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from dataclasses-json<0.7,>=0.5.7->langchain-community>=0.2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (0.9.0)
Requirement already satisfied: httpcore==1.* in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from httpx<1,>=0.23.0->openai>=1.63->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (1.0.9)
Requirement already satisfied: h11>=0.16 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai>=1.63->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (0.16.0)
Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain-core->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading jsonpointer-3.0.0-py2.py3-none-any.whl.metadata (2.3 kB)
Collecting orjson<4.0.0,>=3.9.14 (from langsmith<0.2.0,>=0.1.17->langchain>=0.2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading orjson-3.10.15-cp38-cp38-win_amd64.whl.metadata (42 kB)
Collecting requests-toolbelt<2.0.0,>=1.0.0 (from langsmith<0.2.0,>=0.1.17->langchain>=0.2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading requests_toolbelt-1.0.0-py2.py3-none-any.whl.metadata (14 kB)
Requirement already satisfied: six>=1.5 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from python-dateutil>=2.8.2->pandas>=1.3->paddlex>=3.1.0->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (1.17.0)
Requirement already satisfied: greenlet>=1 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from SQLAlchemy<3,>=1.4->langchain>=0.2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (3.1.1)
Collecting more-itertools (from cssutils->premailer->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr)
  Downloading more_itertools-10.5.0-py3-none-any.whl.metadata (36 kB)
Requirement already satisfied: mypy-extensions>=0.3.0 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain-community>=0.2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (1.1.0)
Requirement already satisfied: propcache>=0.2.0 in c:\users\<USER>\appdata\roaming\python\python38\site-packages (from yarl<2.0,>=1.12.0->aiohttp<4.0.0,>=3.8.3->langchain>=0.2->paddlex[ie,multimodal,ocr,trans]>=3.1.0->paddleocr) (0.2.0)
Downloading paddleocr-3.1.0-py3-none-any.whl (70 kB)
Downloading paddlex-3.1.2-py3-none-any.whl (1.7 MB)
   ---------------------------------------- 1.7/1.7 MB 3.3 MB/s eta 0:00:00
Downloading opencv_contrib_python-*********-cp37-abi3-win_amd64.whl (45.5 MB)
   ---------------------------------------- 45.5/45.5 MB 9.2 MB/s eta 0:00:00
Downloading langchain-0.2.17-py3-none-any.whl (1.0 MB)
   ---------------------------------------- 1.0/1.0 MB 6.8 MB/s eta 0:00:00
Downloading langchain_community-0.2.19-py3-none-any.whl (2.3 MB)
   ---------------------------------------- 2.3/2.3 MB 7.8 MB/s eta 0:00:00
Downloading langchain_core-0.2.43-py3-none-any.whl (397 kB)
Downloading langchain_openai-0.1.25-py3-none-any.whl (51 kB)
Downloading openai-1.95.0-py3-none-any.whl (755 kB)
   ---------------------------------------- 755.6/755.6 kB 8.0 MB/s eta 0:00:00
Using cached tiktoken-0.7.0-cp38-cp38-win_amd64.whl (798 kB)
Downloading beautifulsoup4-4.13.4-py3-none-any.whl (187 kB)
Downloading colorlog-6.9.0-py3-none-any.whl (11 kB)
Downloading ftfy-6.2.3-py3-none-any.whl (43 kB)
Downloading imagesize-1.4.1-py2.py3-none-any.whl (8.8 kB)
Downloading premailer-3.10.0-py2.py3-none-any.whl (19 kB)
Downloading py_cpuinfo-9.0.0-py3-none-any.whl (22 kB)
Downloading ruamel.yaml-0.18.14-py3-none-any.whl (118 kB)
Downloading ujson-5.10.0-cp38-cp38-win_amd64.whl (42 kB)
Downloading async_timeout-4.0.3-py3-none-any.whl (5.7 kB)
Downloading dataclasses_json-0.6.7-py3-none-any.whl (28 kB)
Downloading distro-1.9.0-py3-none-any.whl (20 kB)
Downloading jiter-0.9.1-cp38-cp38-win_amd64.whl (199 kB)
Downloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)
Downloading langchain_text_splitters-0.2.4-py3-none-any.whl (25 kB)
Downloading langsmith-0.1.147-py3-none-any.whl (311 kB)
Downloading ruamel.yaml.clib-0.2.8-cp38-cp38-win_amd64.whl (118 kB)
Downloading soupsieve-2.7-py3-none-any.whl (36 kB)
Downloading tenacity-8.5.0-py3-none-any.whl (28 kB)
Downloading cssselect-1.2.0-py2.py3-none-any.whl (18 kB)
Downloading cssutils-2.11.1-py3-none-any.whl (385 kB)
Downloading jsonpointer-3.0.0-py2.py3-none-any.whl (7.6 kB)
Downloading marshmallow-3.22.0-py3-none-any.whl (49 kB)
Downloading orjson-3.10.15-cp38-cp38-win_amd64.whl (133 kB)
Downloading requests_toolbelt-1.0.0-py2.py3-none-any.whl (54 kB)
Downloading more_itertools-10.5.0-py3-none-any.whl (60 kB)
Building wheels for collected packages: GPUtil
  Building wheel for GPUtil (setup.py): started
  Building wheel for GPUtil (setup.py): finished with status 'done'
  Created wheel for GPUtil: filename=gputil-1.4.0-py3-none-any.whl size=7406 sha256=80d68d5eb050a960a818080a0cec2e905493b96030f314681456f1e04aa0670d
  Stored in directory: c:\users\<USER>\appdata\local\pip\cache\wheels\ba\03\bb\7a97840eb54479b328672e15a536e49dc60da200fb21564d53
Successfully built GPUtil
Installing collected packages: py-cpuinfo, GPUtil, ujson, tenacity, soupsieve, ruamel.yaml.clib, orjson, opencv-contrib-python, more-itertools, marshmallow, jsonpointer, jiter, imagesize, ftfy, distro, cssselect, colorlog, async-timeout, tiktoken, ruamel.yaml, requests-toolbelt, jsonpatch, dataclasses-json, cssutils, beautifulsoup4, premailer, paddlex, openai, langsmith, langchain-core, langchain-text-splitters, langchain-openai, langchain, langchain-community, paddleocr
  Attempting uninstall: opencv-contrib-python
    Found existing installation: opencv-contrib-python *********
    Uninstalling opencv-contrib-python-*********:
      Successfully uninstalled opencv-contrib-python-*********
  Attempting uninstall: async-timeout
    Found existing installation: async-timeout 5.0.1
    Uninstalling async-timeout-5.0.1:
      Successfully uninstalled async-timeout-5.0.1
  Attempting uninstall: tiktoken
    Found existing installation: tiktoken 0.6.0
    Uninstalling tiktoken-0.6.0:
      Successfully uninstalled tiktoken-0.6.0
Successfully installed GPUtil-1.4.0 async-timeout-4.0.3 beautifulsoup4-4.13.4 colorlog-6.9.0 cssselect-1.2.0 cssutils-2.11.1 dataclasses-json-0.6.7 distro-1.9.0 ftfy-6.2.3 imagesize-1.4.1 jiter-0.9.1 jsonpatch-1.33 jsonpointer-3.0.0 langchain-0.2.17 langchain-community-0.2.19 langchain-core-0.2.43 langchain-openai-0.1.25 langchain-text-splitters-0.2.4 langsmith-0.1.147 marshmallow-3.22.0 more-itertools-10.5.0 openai-1.95.0 opencv-contrib-python-********* orjson-3.10.15 paddleocr-3.1.0 paddlex-3.1.2 premailer-3.10.0 py-cpuinfo-9.0.0 requests-toolbelt-1.0.0 ruamel.yaml-0.18.14 ruamel.yaml.clib-0.2.8 soupsieve-2.7 tenacity-8.5.0 tiktoken-0.7.0 ujson-5.10.0
